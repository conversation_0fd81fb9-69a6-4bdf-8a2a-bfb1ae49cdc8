﻿#ifndef IO_TABLE_VIEW_H
#define IO_TABLE_VIEW_H

#include <QAction>
#include <QDebug>
#include <QFile>
#include <QMenu>
#include <QStandardItemModel>
#include <QTranslator>
#include <QWidget>

#include "../io_convert_xls/io_convert_xls.h"
#include "../io_plot_widget/io_plot_widget.h"
#include "../io_xml_parser/io_xml_parser.h"
#include "io_custom_widgets_global.h"

class MyTableModel;

namespace Ui {
class IoTableView;
}

class IO_CUSTOM_WIDGETSSHARED_EXPORT IoTableView : public QWidget
{
    Q_OBJECT

public:
    explicit IoTableView(QWidget* parent = 0);
    ~IoTableView();

private:
    Ui::IoTableView* ui;

public:
    void set_title_list(QVector<QString> title_list);
    void set_column_width(int index, int width);
    void set_resize_stretch_mode();
    void set_max_row_count(int max_count);
    void set_selection_mode_single();
    //-------------------------------------------------
    void append_row(QVector<QString> row_data);
    void delete_last_row();
    void delete_all_row();
    void scroll_to_bottom();
    bool exists_item(QString item);   // 判断单元格中是否有item
    //-------------------------------------------------
    int get_row_count();
    int get_column_count();
    //-------------------------------------------------
    QVector<QString>          get_selected_row();
    QVector<QVector<QString>> get_selected_rows();      // QTableView::SelectRows模式可用
    QVector<QVector<QString>> get_selected_rows_ex();   // QTableView::SelectItems模式可用
    QVector<QVector<QString>> get_all_row();
    QStringList               get_title_list();
    //-------------------------------------------------
    void export_to_csv(QString csv_file, QString delimiter);   // csv路径,分隔符
    void export_to_xls(QString xls_file);
    //-------------------------------------------------


private:
    void init_view();
    void init_menu();
    void init_timer();
    void init_connect();
    //-------------------------------------------------
signals:
    void sig_selected_row(QVector<QString> row_data);
    void sig_doubleclick_row(QVector<QString> row_data);
    void sig_export_complted(QString file);
    void sig_column_moved(QStringList titles);

private:
    MyTableModel* my_table_model_ = nullptr;
    QMenu*        context_menu_   = nullptr;
    QAction*      action_plot_    = nullptr;
    QTimer*       timer_          = nullptr;    // 自动滚动用
};

#endif   // IO_TABLE_VIEW_H
