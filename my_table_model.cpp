﻿#include "my_table_model.h"
#include <QDebug>

MyTableModel::MyTableModel(QObject *parent)
{
    max_row_count_ = 0;
}

int MyTableModel::rowCount(const QModelIndex &parent) const
{
    int count = model_data_.count();
    return count;
}

int MyTableModel::columnCount(const QModelIndex &parent) const
{
    int count = model_title_list_.count();
    return count;
}

QVariant MyTableModel::data(const QModelIndex &index, int role) const
{
    if(!index.isValid() || index.column()>=columnCount() || index.row()>=rowCount()){
        return QVariant();
    }
    int row = index.row();
    int column = index.column();

    switch(role)
    {
    case Qt::DisplayRole:                   // 显示数据用
        return model_data_[row][column];
        break;

    case Qt::EditRole:
        return model_data_[row][column];
        break;

    case Qt::TextAlignmentRole:             // 对齐处理
        return Qt::AlignCenter;
        break;

    default:
        return QVariant();
        break;
    }
    return QVariant();
}

bool MyTableModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if(role != Qt::EditRole || !index.isValid() || index.row()>=rowCount() || index.column()>=columnCount()){
        return false;
    }
    int row = index.row();
    int column = index.column();

    model_data_[row][column] = value.toString();

    emit dataChanged(index, index);
    return true;
}

QVariant MyTableModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (role == Qt::DisplayRole && orientation == Qt::Horizontal){
        if (section < model_title_list_.count()) {
            return model_title_list_[section];
        }
    }
    return QAbstractItemModel::headerData(section, orientation, role);
}

bool MyTableModel::insertRows(int row, int count, const QModelIndex &parent)
{
    // 起始行row超限时，修正到两端插入
    if(row > rowCount()){
        row = rowCount();
    }
    if(row < 0){
        row = 0;
    }

    // 需要将修改部分的代码使用begin和end函数包起来
    beginInsertRows(parent, row, row+count-1);

    // 添加数据
    for(int i = 0; i < count; ++i){
        //bookList.insert(bookList.begin()+row+i, Book());

        int title_cout = model_title_list_.count();
        QVector<QString> column_count(title_cout);
        model_data_.insert(model_data_.begin()+row+i, column_count); //☆重要☆: column_count也可以是类型,决定插入每行数据的列数
    }

    endInsertRows();

    // 发送信号
    emit dataChanged(createIndex(row, 0), createIndex(row+count-1, columnCount()-1));
    return true;
}

bool MyTableModel::removeRows(int row, int count, const QModelIndex &parent)
{
    if(row < 0 || row >= rowCount() || row + count > rowCount()){
        return false;
    }

    // 需要将修改部分的代码使用begin和end函数包起来
    beginRemoveRows(parent, row, row+count-1);

    // 删除数据
    for(int i = 0; i < count; ++i){
        //bookList.remove(row);
        model_data_.remove(row);
    }

    endRemoveRows();

    return true;
}

Qt::ItemFlags MyTableModel::flags(const QModelIndex &index) const
{
    return Qt::ItemIsEditable | QAbstractTableModel::flags(index);
}

void MyTableModel::set_title_list(QVector<QString> titles)
{
    model_title_list_ = titles;
    emit headerDataChanged(Qt::Horizontal,0,model_title_list_.count());
}

void MyTableModel::set_max_row_count(int max_count)
{
    max_row_count_ = max_count;
}

void MyTableModel::append_row(QVector<QString> row_data)
{
    // 不限制行数,正常追加行数据
    if(max_row_count_ <= 0){
        insertRow(rowCount());
        model_data_[rowCount()-1] = row_data;
        return;
    }
    // 保留最新N行(覆盖)
    if(model_data_.size() - 1 >= max_row_count_){
        insertRow(rowCount());
        model_data_[rowCount()-1] = row_data;
        delete_first_row();
    }
    else{
        insertRow(rowCount());
        model_data_[rowCount()-1] = row_data;
    }
}

void MyTableModel::delete_first_row()
{
    removeRow(0);
}

void MyTableModel::delete_last_row()
{
    removeRow(rowCount()-1);
}

void MyTableModel::delete_all_row()
{
    int count = rowCount();
    removeRows(0,count);
}

QVector<QString> MyTableModel::get_title_list()
{
    return model_title_list_;
}

QVector<QVector<QString>> MyTableModel::get_model_data()
{
    return model_data_;
}
