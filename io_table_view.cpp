﻿#include "io_table_view.h"
#include "my_table_model.h"
#include "ui_io_table_view.h"


IoTableView::IoTableView(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::IoTableView)
{
    ui->setupUi(this);

    init_view();
    init_menu();
    init_timer();
    init_connect();
}

IoTableView::~IoTableView() { delete ui; }

void IoTableView::init_view()
{
    my_table_model_ = new MyTableModel;
    ui->tableView->setModel(my_table_model_);

    // 设置水平表头的列宽调整模式为交互式，允许用户手动调整列宽。
    ui->tableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    // 使列标题可点击，通常用于选择列或触发相关操作。
    ui->tableView->horizontalHeader()->setSectionsClickable(true);
    // 禁用表格项的编辑，用户无法直接在表格中修改数据。
    ui->tableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    // 右键菜单
    ui->tableView->setContextMenuPolicy(Qt::CustomContextMenu);
    // 隐藏QTableView 的垂直表头（即行号)
    // ui->tableView->verticalHeader()->hide();

    // 启用交替行颜色，使表格的行在视觉上更易区分，提高可读性。
    ui->tableView->setAlternatingRowColors(true);

    // 设置选择模式为扩展选择, 选择行为为单元格选择
    // ui->tableView->setSelectionMode(QTableView::SingleSelection);       // 仅单选
    ui->tableView->setSelectionMode(QTableView::ExtendedSelection);   // 允许多选
    //
    // ui->tableView->setSelectionBehavior(QTableView::SelectRows);     // 按行选中
    ui->tableView->setSelectionBehavior(QTableView::SelectItems);   // 按单元格选中

    // ========== 关键修改 ==========
    // 使列可拖动排序（必须设置表头，而非 QTableView 本身的拖放）
    ui->tableView->horizontalHeader()->setSectionsMovable(true);
    ui->tableView->horizontalHeader()->setDragEnabled(true);

    // 可选：禁用单元格内容的拖放（如果不需要）
    ui->tableView->setDragEnabled(false);
    ui->tableView->setDragDropMode(QAbstractItemView::NoDragDrop);
}


void IoTableView::init_menu()
{
    action_plot_ = new QAction(tr("查看波形"), ui->tableView);

    context_menu_ = new QMenu(ui->tableView);
    context_menu_->addAction(action_plot_);
}

void IoTableView::init_timer()
{
    timer_ = new QTimer;
    timer_->start(1000);
}

void IoTableView::init_connect()
{
    // 单击选中行
    connect(ui->tableView, &QTableView::clicked, this, [=](const QModelIndex& index) {
        QVector<QString> row_data = get_selected_row();
        emit             sig_selected_row(row_data);
    });

    // 双击选中行
    connect(ui->tableView, &QTableView::doubleClicked, this, [=]() {
        QVector<QString> row_data = get_selected_row();
        emit             sig_doubleclick_row(row_data);
    });

    // 右键菜单
    connect(ui->tableView, &QTableView::customContextMenuRequested, this, [=](const QPoint& pos) {
        // 数据项有效才显示菜单
        QModelIndex index = ui->tableView->indexAt(pos);
        if (!index.isValid()) {
            return;
        }
        context_menu_->exec(QCursor::pos());
    });

    // 触发菜单
    connect(action_plot_, &QAction::triggered, this, [=]() {
        // 获取选中的单元格
        QModelIndexList cells = ui->tableView->selectionModel()->selectedIndexes();
        if (cells.isEmpty()) {
            return;
        }
        // 创建 QHash 用于存储图表数据
        QHash<QString, QList<QPointF>> graph_data;

        // 计算所有坐标点中的min_x,max_x和min_y和min_y坐标为初始坐标系
        float min_x = std::numeric_limits<float>::max();
        float max_x = std::numeric_limits<float>::lowest();
        float min_y = std::numeric_limits<float>::max();
        float max_y = std::numeric_limits<float>::lowest();

        // 遍历所有选中的单元格
        for (const QModelIndex& cell : cells) {
            // 获取单元格的行和数据值，将它们存为 QPoint
            bool  ok = false;
            int   x  = cell.row();
            float y  = cell.data().toFloat(&ok);
            // 过滤非数字单元格
            if (!ok) {
                continue;
            }
            // 获取列标题作为 key
            QString header = my_table_model_->headerData(cell.column(), Qt::Horizontal).toString();
            QPointF point(x, y);
            // 可以直接使用 [] 操作符插入数据
            // 它会自动处理插入或追加操作,不需要显式地检查 graph_data.contains(header)
            graph_data[header].append(point);

            // 更新最小值和最大值
            min_x = std::min(min_x, static_cast<float>(x));
            max_x = std::max(max_x, static_cast<float>(x));
            min_y = std::min(min_y, y);
            max_y = std::max(max_y, y);
        }

        IoPlotWidget* plot = new IoPlotWidget(this);
        plot->init("");
        plot->show();

        //
        for (auto it = graph_data.constBegin(); it != graph_data.constEnd(); ++it) {
            //
            int graph_id = plot->add_graph();

            // 设置线条名称及图例名称
            plot->set_graph_name(graph_id, it.key());

            // 输出每个列对应的 QPoint 列表
            const QList<QPointF>& points = it.value();

            //
            for (const QPointF& point : points) {
                plot->add_point(graph_id, point.x(), point.y());
                qDebug() << "column:" << it.key() << "Row:" << point.x() << "Value:" << point.y();
            }
        }

        // 如果所有X点相同，增加一个单位范围
        if (min_x == max_x) {
            max_x += 1;
        }

        // 如果所有Y点相同，增加一个单位范围
        if (min_y == max_y) {
            max_y += 1;
        }

        // 添加边距
        float x_margin = (max_x - min_x) * 0.2;   // 20% 的边距
        float y_margin = (max_y - min_y) * 0.2;   // 20% 的边距

        // 设置轴范围，加上边距
        plot->set_x_range(min_x - x_margin, max_x + x_margin);
        plot->set_y_range(min_y - y_margin, max_y + y_margin);

        // 设置初始轴范围
        plot->set_x_init(min_x - x_margin, max_x + x_margin);
        plot->set_y_init(min_y - y_margin, max_y + y_margin);
    });

    // 鼠标拖动列
    connect(ui->tableView->horizontalHeader(), &QHeaderView::sectionMoved, this, [=](int logicalIndex, int oldVisualIndex, int newVisualIndex) {
        QStringList titles = get_title_list();
        emit        sig_column_moved(titles);
    });

    // 绑定滚动条值变化事件
    QScrollBar* scrollBar = ui->tableView->verticalScrollBar();
    connect(scrollBar, &QScrollBar::valueChanged, this, [=](int value) {
        int max       = scrollBar->maximum();
        int threshold = 1;   // 判定用户距离底部 N 像素也认为“在底部”

        if (max - value <= threshold) {
            // 到达底部，启动自动滚动
            if (!timer_->isActive()) {
                timer_->start();
            }
        }
        else {
            // 用户向上翻动，关闭自动滚动
            if (timer_->isActive()) {
                timer_->stop();
            }
        }
    });

    //
    connect(timer_, &QTimer::timeout, this, [=]() {
        //
        ui->tableView->scrollToBottom();
    });
}
void IoTableView::set_title_list(QVector<QString> title_list)
{
    //
    my_table_model_->set_title_list(title_list);
}

void IoTableView::set_column_width(int index, int width)
{
    //
    ui->tableView->setColumnWidth(index, width);
}

void IoTableView::append_row(QVector<QString> row_data)
{
    //
    my_table_model_->append_row(row_data);
}

void IoTableView::delete_last_row()
{
    //
    my_table_model_->delete_last_row();
}

void IoTableView::delete_all_row()
{
    //
    my_table_model_->delete_all_row();
}

void IoTableView::scroll_to_bottom()
{
    // 如果每插入一行执行一次该函数会大大大降低性能
    ui->tableView->scrollToBottom();
}

bool IoTableView::exists_item(QString item)
{
    QVector<QVector<QString>> rows = get_all_row();

    // 遍历所有单元格
    for (const QVector<QString>& row : rows) {
        for (const QString& cell : row) {
            if (cell.contains(item)) {
                return true;
            }
        }
    }
    return false;
}

int IoTableView::get_row_count()
{
    int count = my_table_model_->rowCount();
    return count;
}

int IoTableView::get_column_count()
{
    int count = my_table_model_->columnCount();
    return count;
}

QVector<QString> IoTableView::get_selected_row()
{
    QVector<QString> result;

    int row          = ui->tableView->currentIndex().row();
    int column_count = get_column_count();

    if (row < 0) {
        qDebug() << "ITV选中行:" << row;
        return result;
    }

    QAbstractItemModel* model = ui->tableView->model();

    for (int i = 0; i < column_count; i++) {
        QModelIndex index = model->index(row, i);
        QVariant    data  = model->data(index).toString();
        result << data.toString();
    }
    return result;
}

/*
    必须用这两个模式才能返回选中的多行
    ui->tableView->setSelectionBehavior(QTableView::SelectRows);
    ui->tableView->setSelectionMode(QTableView::ExtendedSelection);
 */
QVector<QVector<QString>> IoTableView::get_selected_rows()
{
    QVector<QVector<QString>> selected_records;
    QVector<QString>          record;

    QAbstractItemModel*  model       = ui->tableView->model();
    QItemSelectionModel* selectModel = ui->tableView->selectionModel();

    QModelIndexList selectedRows = selectModel->selectedRows();

    qDebug() << selectedRows;

    int column_count = get_column_count();

    for (const QModelIndex& rowIndex : selectedRows) {
        int row = rowIndex.row();
        record.clear();
        for (int column = 0; column < column_count; ++column) {
            QModelIndex index = model->index(row, column);
            record << model->data(index).toString();
        }
        selected_records << record;
    }

    return selected_records;
}
/*
    下载多个选中文件时用到
    此函数用这两个模式也可以获取到选中的行
    ui->tableView->setSelectionBehavior(QTableView::SelectItems);
    ui->tableView->setSelectionMode(QTableView::ExtendedSelection);
 */
QVector<QVector<QString>> IoTableView::get_selected_rows_ex()
{
    QSet<int>                 unique_rows;   // 存储唯一的行号
    QVector<QVector<QString>> selected_records;

    QAbstractItemModel*  model           = ui->tableView->model();
    QItemSelectionModel* selectModel     = ui->tableView->selectionModel();
    QModelIndexList      selectedIndexes = selectModel->selectedIndexes();

    // 收集选中单元格所在行的行号
    for (const QModelIndex& index : selectedIndexes) {
        unique_rows.insert(index.row());
    }

    int column_count = model->columnCount();

    // 遍历这些唯一行号，提取整行数据
    for (int row : unique_rows) {
        QVector<QString> record;
        for (int column = 0; column < column_count; ++column) {
            QModelIndex index = model->index(row, column);
            record << model->data(index).toString();
        }
        selected_records << record;
    }

    return selected_records;
}

QVector<QVector<QString>> IoTableView::get_all_row()
{
    QVector<QVector<QString>> all_record;
    QVector<QString>          record;

    QAbstractItemModel* model = ui->tableView->model();

    int row_count    = get_row_count();
    int column_count = get_column_count();

    for (int row = 0; row < row_count; row++) {
        record.clear();
        //
        for (int column = 0; column < column_count; column++) {
            QModelIndex index = model->index(row, column);
            record << model->data(index).toString();
        }
        all_record << record;
    }
    return all_record;
}

QStringList IoTableView::get_title_list()
{
    QHeaderView*        header = ui->tableView->horizontalHeader();
    QAbstractItemModel* model  = ui->tableView->model();

    QStringList titles;
    for (int visualPos = 0; visualPos < header->count(); ++visualPos) {
        int logicalIdx = header->logicalIndex(visualPos);
        titles << model->headerData(logicalIdx, Qt::Horizontal).toString();
    }
    return titles;
}

void IoTableView::export_to_csv(QString csv_file, QString delimiter)
{
    QVector<QString>          model_title_list = my_table_model_->get_title_list();
    QVector<QVector<QString>> model_data       = my_table_model_->get_model_data();
    //
    if (model_data.isEmpty() || model_title_list.isEmpty()) {
        return;
    }
    // 文件
    QFile       file(csv_file);
    QTextStream stream(&file);
    if (file.open(QIODevice::WriteOnly | QIODevice::Truncate) == false) {
        return;
    }

    // csv写入标题
    for (QString title : model_title_list) {
        if (title.isEmpty()) {
            continue;
        }
        stream << title << delimiter;
    }
    stream << endl;

    // csv写入行数据
    for (QVector<QString> row_data : model_data) {
        if (row_data.isEmpty()) {
            continue;
        }
        for (QString cell_text : row_data) {
            stream << cell_text << delimiter;
        }
        stream << endl;
    }
    // 保存到文件
    file.close();

    // 导出完成
    emit sig_export_complted(csv_file);
}

void IoTableView::export_to_xls(QString xls_file)
{
    IoConvertXls              xls_parser;
    QVector<QString>          model_title_list = my_table_model_->get_title_list();
    QVector<QVector<QString>> model_data       = my_table_model_->get_model_data();

    if (model_data.isEmpty() || model_title_list.isEmpty()) {
        return;
    }

    // xls标题
    xls_parser.write_record(model_title_list);

    // 行数据
    for (QVector<QString> row_data : model_data) {
        xls_parser.write_record(row_data);
    }

    // 保存xls
    // QString dir = IoString::get_parent_dir(csv_file);
    // QString file_name = IoString::get_file_name_no_extension(csv_file);
    // QString xls_file = QString("%1/%2.xls").arg(dir).arg(file_name);
    xls_parser.save_xls(xls_file);

    // 导出完成
    emit sig_export_complted(xls_file);
}

void IoTableView::set_resize_stretch_mode()
{
    //
    ui->tableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
}

void IoTableView::set_max_row_count(int max_count)
{
    if (my_table_model_ == nullptr) {
        return;
    }
    my_table_model_->set_max_row_count(max_count);
}

void IoTableView::set_selection_mode_single()
{
    ui->tableView->setSelectionMode(QTableView::SingleSelection);   // 仅单选
}
