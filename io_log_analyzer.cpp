#include "io_log_analyzer.h"
#include "ui_io_log_analyzer.h"

IoLogAnalyzer::IoLogAnalyzer(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::IoLogAnalyzer)
{
    ui->setupUi(this);
    init_connect();
}

IoLogAnalyzer::~IoLogAnalyzer()
{
    delete ui;
}

void IoLogAnalyzer::init(QString project)
{
    project_ = project;
}

void IoLogAnalyzer::init_connect()
{
    // 连接选择文件按钮的点击事件
    connect(ui->PB_Select_File, &QPushButton::clicked, this, &IoLogAnalyzer::on_select_file_clicked);
}

void IoLogAnalyzer::on_select_file_clicked()
{
    // 打开文件对话框选择CSV文件
    QString file_path = QFileDialog::getOpenFileName(
        this,
        tr("选择CSV文件"),
        "",
        tr("CSV文件 (*.csv);;所有文件 (*)")
    );

    if (!file_path.isEmpty()) {
        // 显示选择的文件路径
        ui->LE_Log_Path->setText(file_path);

        // 加载CSV文件
        load_csv_file(file_path);
    }
}

void IoLogAnalyzer::load_csv_file(const QString& file_path)
{
    QFile file(file_path);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开文件: %1").arg(file_path));
        return;
    }

    QTextStream in(&file);
    in.setCodec("UTF-8"); // 设置编码为UTF-8
    QString content = in.readAll();
    file.close();

    if (content.isEmpty()) {
        QMessageBox::information(this, tr("提示"), tr("文件为空"));
        return;
    }

    // 解析CSV内容
    QVector<QVector<QString>> csv_data = parse_csv_content(content);

    if (csv_data.isEmpty()) {
        QMessageBox::information(this, tr("提示"), tr("CSV文件没有有效数据"));
        return;
    }

    // 清空现有数据
    ui->ITV_csv->delete_all_row();

    // 设置表头（第一行作为标题）
    if (!csv_data.isEmpty()) {
        ui->ITV_csv->set_title_list(csv_data.first());

        // 添加数据行（从第二行开始）
        for (int i = 1; i < csv_data.size(); ++i) {
            ui->ITV_csv->append_row(csv_data[i]);
        }
    }

    qDebug() << "CSV文件加载完成，共" << (csv_data.size() - 1) << "行数据";
}

QVector<QVector<QString>> IoLogAnalyzer::parse_csv_content(const QString& content)
{
    QVector<QVector<QString>> result;
    QStringList lines = content.split('\n', QString::SkipEmptyParts);

    for (const QString& line : lines) {
        if (line.trimmed().isEmpty()) {
            continue;
        }

        QVector<QString> row;
        QStringList fields = line.split(',');

        for (const QString& field : fields) {
            // 去除字段两端的空白字符和引号
            QString cleaned_field = field.trimmed();
            if (cleaned_field.startsWith('"') && cleaned_field.endsWith('"')) {
                cleaned_field = cleaned_field.mid(1, cleaned_field.length() - 2);
            }
            row.append(cleaned_field);
        }

        if (!row.isEmpty()) {
            result.append(row);
        }
    }

    return result;
}

