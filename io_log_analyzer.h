#ifndef IO_LOG_ANALYZER_H
#define IO_LOG_ANALYZER__H

#include "io_custom_component_global.h"
#include <QWidget>
#include <QFileDialog>
#include <QTextStream>
#include <QMessageBox>
#include <QDebug>

#include "../io_file_parser/io_file_parser.h"
#include "../io_xml_parser/io_xml_parser.h"

#include "../io_base_controls/io_base_controls.h"
#include "../io_custom_component/io_parsing_logs_widget/io_parsing_logs_widget.h"

namespace Ui {
class IoLogAnalyzer;
}

class IO_CUSTOM_COMPONENTSHARED_EXPORT IoLogAnalyzer : public IoBaseControls
{
    Q_OBJECT

public:
    explicit IoLogAnalyzer(QWidget *parent = 0);
    ~IoLogAnalyzer();

private:
    Ui::IoLogAnalyzer *ui;

public:
    void init(QString project);

private slots:
    void on_select_file_clicked();

private:
    void init_connect();
    void load_csv_file(const QString& file_path);
    QVector<QVector<QString>> parse_csv_content(const QString& content);

private:
    QString project_;
    IoXmlParser xml_parser_;

};

#endif // KTX_PARSING_LOG_H
