﻿#ifndef MYTABLEMODEL_H
#define MYTABLEMODEL_H

#include <QObject>
#include <QAbstractTableModel>

class MyTableModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit MyTableModel(QObject *parent = 0);

    int rowCount(const QModelIndex &parent = QModelIndex())const override;
    int columnCount(const QModelIndex &parent = QModelIndex())const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole)const override;

    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    bool insertRows(int row, int count, const QModelIndex &parent = QModelIndex()) override;
    bool removeRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual Qt::ItemFlags flags(const QModelIndex &index) const override;

    void set_title_list(QVector<QString> titles);
    void set_max_row_count(int max_count);
    void append_row(QVector<QString> row_data);
    void delete_first_row();
    void delete_last_row();
    void delete_all_row();

    QVector<QString> get_title_list();
    QVector<QVector<QString>> get_model_data();

private:
    QVector<QVector<QString>> model_data_;
    QVector<QString> model_title_list_;
    int max_row_count_;
};

#endif // MYTABLEMODEL_H
